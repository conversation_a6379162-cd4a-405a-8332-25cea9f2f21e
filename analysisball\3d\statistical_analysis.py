#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3D彩票统计分析模块
功能：
1. 频率分析 - 各数字出现频率统计
2. 分布分析 - 数字分布的统计特征
3. 相关性分析 - 百位、十位、个位之间的相关性
4. 热度分析 - 冷热号码分析
5. 间隔分析 - 数字出现间隔统计
6. 组合分析 - 数字组合模式分析
"""

import numpy as np
import pandas as pd
import sqlite3
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from typing import Dict, List, Tuple, Optional
from pathlib import Path
import logging
from collections import Counter, defaultdict

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class Statistical3DAnalyzer:
    """3D彩票统计分析类"""

    def __init__(self, data_source: str = "analysisball/3d/3d_data.db"):
        """
        初始化统计分析器

        Args:
            data_source: 数据源路径
        """
        self.data_source = Path(data_source)
        self.data = None

    def load_data(self) -> pd.DataFrame:
        """加载3D彩票数据"""
        try:
            if self.data_source.suffix == '.db':
                with sqlite3.connect(self.data_source) as conn:
                    query = '''
                        SELECT period, date, hundreds, tens, units, number_str
                        FROM lottery_3d
                        ORDER BY period ASC
                    '''
                    self.data = pd.read_sql_query(query, conn)
            elif self.data_source.suffix in ['.xlsx', '.xls']:
                self.data = pd.read_excel(self.data_source)
                if '期号' in self.data.columns:
                    self.data = self.data.rename(columns={
                        '期号': 'period', '日期': 'date',
                        '百位': 'hundreds', '十位': 'tens', '个位': 'units'
                    })

            # 确保数据类型正确
            self.data['hundreds'] = self.data['hundreds'].astype(int)
            self.data['tens'] = self.data['tens'].astype(int)
            self.data['units'] = self.data['units'].astype(int)

            logger.info(f"成功加载 {len(self.data)} 条3D彩票数据")
            return self.data

        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            raise

    def frequency_analysis(self) -> Dict[str, Dict]:
        """
        频率分析 - 统计各位置数字的出现频率

        Returns:
            各位置的频率统计结果
        """
        if self.data is None:
            self.load_data()

        positions = ['hundreds', 'tens', 'units']
        position_names = ['百位', '十位', '个位']

        results = {}

        for pos, pos_name in zip(positions, position_names):
            # 计算频率
            frequency = self.data[pos].value_counts().sort_index()
            total_count = len(self.data)

            # 计算概率
            probability = frequency / total_count

            # 理论概率（均匀分布）
            theoretical_prob = 1.0 / 10

            # 计算偏差
            deviation = probability - theoretical_prob

            # 卡方检验
            expected = [total_count * theoretical_prob] * 10
            observed = [frequency.get(i, 0) for i in range(10)]
            chi2_stat, p_value = stats.chisquare(observed, expected)

            results[pos] = {
                'position_name': pos_name,
                'frequency': frequency.to_dict(),
                'probability': probability.to_dict(),
                'deviation_from_uniform': deviation.to_dict(),
                'chi2_test': {
                    'statistic': chi2_stat,
                    'p_value': p_value,
                    'is_uniform': p_value > 0.05
                },
                'statistics': {
                    'most_frequent': frequency.idxmax(),
                    'least_frequent': frequency.idxmin(),
                    'max_frequency': frequency.max(),
                    'min_frequency': frequency.min(),
                    'variance': probability.var(),
                    'entropy': -np.sum(probability * np.log2(probability + 1e-10))
                }
            }

        return results

    def correlation_analysis(self) -> Dict[str, any]:
        """
        相关性分析 - 分析百位、十位、个位之间的相关性

        Returns:
            相关性分析结果
        """
        if self.data is None:
            self.load_data()

        # 计算皮尔逊相关系数
        positions_data = self.data[['hundreds', 'tens', 'units']]
        correlation_matrix = positions_data.corr()

        # 计算斯皮尔曼相关系数
        spearman_corr = positions_data.corr(method='spearman')

        # 计算肯德尔相关系数
        kendall_corr = positions_data.corr(method='kendall')

        # 独立性检验（卡方检验）
        independence_tests = {}

        pairs = [('hundreds', 'tens'), ('hundreds', 'units'), ('tens', 'units')]
        for pos1, pos2 in pairs:
            # 创建列联表
            contingency_table = pd.crosstab(self.data[pos1], self.data[pos2])

            # 卡方检验
            chi2, p_value, dof, expected = stats.chi2_contingency(contingency_table)

            independence_tests[f"{pos1}_{pos2}"] = {
                'chi2_statistic': chi2,
                'p_value': p_value,
                'degrees_of_freedom': dof,
                'is_independent': p_value > 0.05
            }

        return {
            'pearson_correlation': correlation_matrix.to_dict(),
            'spearman_correlation': spearman_corr.to_dict(),
            'kendall_correlation': kendall_corr.to_dict(),
            'independence_tests': independence_tests
        }