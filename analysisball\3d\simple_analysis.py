#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3D彩票简化分析模块
功能：
1. 基础频率统计
2. 简单马尔科夫链分析
3. 数字转移概率计算
4. 基础预测功能
"""

import json
import os
from collections import defaultdict, Counter
from typing import Dict, List, Tuple

class Simple3DAnalyzer:
    """简化版3D彩票分析器"""

    def __init__(self, data_file: str = "analysisball/3d/3d.xlsx"):
        """
        初始化分析器

        Args:
            data_file: 数据文件路径
        """
        self.data_file = data_file
        self.data = []
        self.loaded = False

    def load_data_from_excel(self):
        """从Excel文件加载数据（简化版本，不使用pandas）"""
        try:
            # 由于没有pandas，我们创建一些示例数据来演示
            # 在实际使用中，您需要安装pandas或使用其他方法读取Excel
            print("注意：由于环境限制，使用示例数据进行演示")

            # 生成一些示例3D彩票数据
            import random
            random.seed(42)  # 固定种子以获得可重复的结果

            for i in range(1000):  # 生成1000期数据
                period = f"2024{i:03d}"
                date = f"2024-01-{(i % 30) + 1:02d}"
                hundreds = random.randint(0, 9)
                tens = random.randint(0, 9)
                units = random.randint(0, 9)

                self.data.append({
                    'period': period,
                    'date': date,
                    'hundreds': hundreds,
                    'tens': tens,
                    'units': units,
                    'number_str': f"{hundreds}{tens}{units}"
                })

            self.loaded = True
            print(f"成功加载 {len(self.data)} 条示例数据")

        except Exception as e:
            print(f"加载数据失败: {e}")
            raise

    def frequency_analysis(self) -> Dict[str, Dict]:
        """频率分析"""
        if not self.loaded:
            self.load_data_from_excel()

        positions = ['hundreds', 'tens', 'units']
        position_names = ['百位', '十位', '个位']

        results = {}

        for pos, pos_name in zip(positions, position_names):
            # 统计频率
            frequency = Counter([item[pos] for item in self.data])
            total_count = len(self.data)

            # 计算概率
            probability = {k: v / total_count for k, v in frequency.items()}

            # 找出最热和最冷的数字
            most_frequent = max(frequency, key=frequency.get)
            least_frequent = min(frequency, key=frequency.get)

            results[pos] = {
                'position_name': pos_name,
                'frequency': dict(frequency),
                'probability': probability,
                'most_frequent': most_frequent,
                'least_frequent': least_frequent,
                'max_frequency': frequency[most_frequent],
                'min_frequency': frequency[least_frequent]
            }

        return results

    def build_transition_matrix(self, position: str) -> Dict[int, Dict[int, float]]:
        """
        构建一阶马尔科夫链转移矩阵

        Args:
            position: 位置 ('hundreds', 'tens', 'units')

        Returns:
            转移矩阵字典
        """
        if not self.loaded:
            self.load_data_from_excel()

        # 获取数字序列
        sequence = [item[position] for item in self.data]

        # 统计转移次数
        transition_counts = defaultdict(lambda: defaultdict(int))

        for i in range(len(sequence) - 1):
            current_state = sequence[i]
            next_state = sequence[i + 1]
            transition_counts[current_state][next_state] += 1

        # 转换为概率矩阵
        transition_matrix = {}

        for current_state in range(10):
            transition_matrix[current_state] = {}
            total_transitions = sum(transition_counts[current_state].values())

            if total_transitions > 0:
                for next_state in range(10):
                    count = transition_counts[current_state][next_state]
                    transition_matrix[current_state][next_state] = count / total_transitions
            else:
                # 如果没有转移数据，使用均匀分布
                for next_state in range(10):
                    transition_matrix[current_state][next_state] = 0.1

        return transition_matrix

    def predict_next_numbers(self, position: str, current_number: int, top_k: int = 3) -> List[Tuple[int, float]]:
        """
        预测下期可能出现的数字

        Args:
            position: 位置
            current_number: 当前数字
            top_k: 返回概率最高的前k个数字

        Returns:
            [(数字, 概率), ...] 按概率降序排列
        """
        transition_matrix = self.build_transition_matrix(position)

        # 获取当前数字的转移概率
        probabilities = transition_matrix[current_number]

        # 按概率排序
        sorted_probs = sorted(probabilities.items(), key=lambda x: x[1], reverse=True)

        return sorted_probs[:top_k]

    def analyze_patterns(self) -> Dict[str, any]:
        """分析数字模式"""
        if not self.loaded:
            self.load_data_from_excel()

        # 分析连号情况
        consecutive_patterns = {
            'ascending': 0,    # 递增 (如: 123)
            'descending': 0,   # 递减 (如: 321)
            'same_all': 0,     # 全相同 (如: 111)
            'same_two': 0,     # 两个相同 (如: 112, 121, 211)
            'other': 0         # 其他
        }

        # 分析奇偶模式
        odd_even_patterns = Counter()

        # 分析大小模式（5以上为大，4以下为小）
        size_patterns = Counter()

        for item in self.data:
            h, t, u = item['hundreds'], item['tens'], item['units']

            # 连号分析
            if h == t == u:
                consecutive_patterns['same_all'] += 1
            elif h == t or t == u or h == u:
                consecutive_patterns['same_two'] += 1
            elif (h + 1 == t and t + 1 == u) or (h == t + 1 and t == u + 1):
                if h < t < u:
                    consecutive_patterns['ascending'] += 1
                else:
                    consecutive_patterns['descending'] += 1
            else:
                consecutive_patterns['other'] += 1

            # 奇偶分析
            odd_even = ''.join(['奇' if x % 2 == 1 else '偶' for x in [h, t, u]])
            odd_even_patterns[odd_even] += 1

            # 大小分析
            size = ''.join(['大' if x >= 5 else '小' for x in [h, t, u]])
            size_patterns[size] += 1

        return {
            'consecutive_patterns': consecutive_patterns,
            'odd_even_patterns': dict(odd_even_patterns),
            'size_patterns': dict(size_patterns)
        }

    def generate_report(self, output_file: str = "analysisball/3d/simple_analysis_report.json"):
        """生成分析报告"""
        print("开始生成3D彩票分析报告...")

        # 频率分析
        print("1. 进行频率分析...")
        frequency_results = self.frequency_analysis()

        # 马尔科夫链分析
        print("2. 构建马尔科夫链转移矩阵...")
        transition_matrices = {}
        for position in ['hundreds', 'tens', 'units']:
            transition_matrices[position] = self.build_transition_matrix(position)

        # 模式分析
        print("3. 分析数字模式...")
        pattern_results = self.analyze_patterns()

        # 预测示例
        print("4. 生成预测示例...")
        predictions = {}
        if self.data:
            last_numbers = {
                'hundreds': self.data[-1]['hundreds'],
                'tens': self.data[-1]['tens'],
                'units': self.data[-1]['units']
            }

            for position, number in last_numbers.items():
                predictions[position] = {
                    'current_number': number,
                    'predictions': self.predict_next_numbers(position, number, top_k=5)
                }

        # 汇总报告
        report = {
            'summary': {
                'total_records': len(self.data),
                'analysis_date': '2024-06-19',
                'data_type': 'simulated'  # 标记这是示例数据
            },
            'frequency_analysis': frequency_results,
            'transition_matrices': transition_matrices,
            'pattern_analysis': pattern_results,
            'predictions': predictions
        }

        # 保存报告
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        print(f"分析报告已保存到: {output_file}")

        # 打印关键结果
        self.print_summary(report)

        return report

    def print_summary(self, report: Dict):
        """打印分析摘要"""
        print("\n" + "="*50)
        print("3D彩票分析摘要")
        print("="*50)

        print(f"数据总量: {report['summary']['total_records']} 期")

        print("\n频率分析结果:")
        for position, data in report['frequency_analysis'].items():
            pos_name = data['position_name']
            most_freq = data['most_frequent']
            least_freq = data['least_frequent']
            print(f"  {pos_name}: 最热号码 {most_freq} ({data['max_frequency']}次), "
                  f"最冷号码 {least_freq} ({data['min_frequency']}次)")

        print("\n模式分析结果:")
        patterns = report['pattern_analysis']
        print(f"  全相同号码: {patterns['consecutive_patterns']['same_all']} 期")
        print(f"  两个相同号码: {patterns['consecutive_patterns']['same_two']} 期")

        print("\n基于马尔科夫链的预测:")
        for position, pred_data in report['predictions'].items():
            pos_name = {'hundreds': '百位', 'tens': '十位', 'units': '个位'}[position]
            current = pred_data['current_number']
            top_pred = pred_data['predictions'][0]
            print(f"  {pos_name}: 当前数字 {current} -> 预测数字 {top_pred[0]} (概率: {top_pred[1]:.3f})")

def main():
    """主函数"""
    analyzer = Simple3DAnalyzer()

    try:
        # 生成分析报告
        report = analyzer.generate_report()

        print("\n分析完成！")
        print("注意：由于环境限制，本次使用的是模拟数据。")
        print("在实际使用中，请确保安装pandas等依赖包，并使用真实的3D彩票数据。")

    except Exception as e:
        print(f"分析过程中出错: {e}")

if __name__ == "__main__":
    main()